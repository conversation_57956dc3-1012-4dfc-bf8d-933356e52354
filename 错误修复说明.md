# 'can't convert float to int' 错误修复说明

## 问题原因

在OpenMV的图像绘制函数中，坐标参数必须是整数类型，但我们的视角补偿计算返回的是浮点数，导致类型转换错误。

## 修复的具体位置

### 1. 绘制函数坐标转换
```python
# 修复前（会报错）
img.draw_cross((original_x, original_y), color=(0, 255, 0))
img.draw_cross((local_x, local_y), color=(255, 0, 0))
img.draw_line((original_x, original_y, local_x, local_y), color=(255, 255, 0))

# 修复后（正确）
img.draw_cross((int(original_x), int(original_y)), color=(0, 255, 0))
img.draw_cross((int(local_x), int(local_y)), color=(255, 0, 0))
img.draw_line((int(original_x), int(original_y), int(local_x), int(local_y)), color=(255, 255, 0))
```

### 2. 色块中心点坐标转换
```python
# 修复前（会报错）
img.draw_cross((blob.cx(), blob.cy()), color=(0, 255, 0))

# 修复后（正确）
img.draw_cross((int(blob.cx()), int(blob.cy())), color=(0, 255, 0))
```

### 3. 补偿后坐标计算
```python
# 修复前（可能产生浮点数）
local_x = original_x + correction_x
local_y = original_y

# 修复后（确保为整数）
local_x = int(original_x + correction_x)
local_y = int(original_y)
```

## 修复的文件位置

在 `openmv方法找矩形跳帧.py` 文件中的以下行：
- 第640行：`blob.cx()` 和 `blob.cy()` 转换为整数
- 第652行：`local_x` 和 `local_y` 确保为整数
- 第655-656行：绘制十字坐标转换为整数
- 第660行：绘制线条坐标转换为整数
- 第678行：备用方案中的 `local_x` 和 `local_y` 确保为整数
- 第681-682行：备用方案中绘制十字坐标转换为整数
- 第686行：备用方案中绘制线条坐标转换为整数

## 为什么会出现这个问题

1. **视角补偿计算**: 补偿算法使用浮点数运算来提高精度
2. **OpenMV绘制函数**: 要求坐标参数必须是整数类型
3. **类型不匹配**: 浮点数坐标传递给需要整数的绘制函数

## 解决方案的优点

1. **保持计算精度**: 补偿算法仍然使用浮点数计算，保证精度
2. **兼容绘制函数**: 在绘制时才转换为整数，满足OpenMV要求
3. **最小化误差**: 使用 `int()` 函数进行截断，误差最小

## 测试建议

修复后请测试以下功能：
1. 视角补偿是否正常工作
2. 绘制的十字和线条是否正确显示
3. 补偿效果是否符合预期
4. 不再出现类型转换错误

## 注意事项

- 使用 `int()` 函数会截断小数部分，可能有1像素的误差
- 这种误差在实际应用中通常可以忽略
- 如果需要更高精度，可以考虑使用 `round()` 函数四舍五入

## 其他可能的类型错误

如果还遇到类似错误，请检查：
1. 所有传递给 `draw_*` 函数的坐标是否为整数
2. 矩形坐标计算是否产生浮点数
3. 数学运算结果是否需要类型转换

## 完整的修复代码示例

```python
# 正确的绘制方式
def safe_draw_cross(img, x, y, color):
    """安全的绘制十字函数，自动转换坐标类型"""
    img.draw_cross((int(x), int(y)), color=color)

def safe_draw_line(img, x1, y1, x2, y2, color):
    """安全的绘制线条函数，自动转换坐标类型"""
    img.draw_line((int(x1), int(y1), int(x2), int(y2)), color=color)

# 使用示例
safe_draw_cross(img, original_x, original_y, (0, 255, 0))
safe_draw_cross(img, local_x, local_y, (255, 0, 0))
safe_draw_line(img, original_x, original_y, local_x, local_y, (255, 255, 0))
```
