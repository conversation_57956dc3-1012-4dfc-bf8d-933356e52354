import time,os,urandom,sys,gc#,image#如果要显示照片，则引用image模块处理图片信息 | 导入必要的模块：时间、操作系统、串口、系统、垃圾回收
from media.display import *
from media.media import *#显示图像需要用到Media的display和media模块
from media.sensor import *
from machine import FPIOA
from machine import Pin
from machine import UART
from ybUtils.YbUart import YbUart
from threshold_setter import ThresholdSetter
# 导入视角补偿配置（如果文件存在）
try:
    from perspective_correction_config import *
    print("已加载视角补偿配置文件")
except ImportError:
    print("未找到视角补偿配置文件，使用默认参数")

##TODO:串口发二进制变量,矩形大小过滤.矩形数组四个角分类,解决blobs 0点画图
#后撤问题，矩形过滤左上角（-1，-1，-1，-1）
#阈值难调问题，矩形识别不到问题
#色块大小过滤
#初始位置归位删除
#corner拆开
#设置图像大小
#学习异常捕获
WIDTH = 320
HEIGHT = 240
roi = (640,300,640,480)  #感兴趣区域
binary = (0, 35)      #图像二值化参数
#颜色识别阈值
red = (0, 100, 31, 127, -128, 127)
red1 = (0, 100, 39, 127, -128, 127)

green = (35, 100, -128, -44, -128, 127)
green1 = (0, 100, -128, -29, -128, 127)
#占空比
DC_x = 0
DC_y = 0
#pid参数
PID_Kp = 0.000003# 比例系数
PID_Ki = -0.00000# 积分系数
PID_Kd = -0.000# 微分系数
PID_PARAMS = (PID_Kp, PID_Ki, PID_Kd)
P_GAIN = -3.3 # 比例增益，增大以提高响应速度
D_GAIN = -0.0 # 微分增益，增大以提供更强的阻尼和预测能力
DEAD_ZONE = 1  # 中心死区，减小以提高精度
PREDICTION_FACTOR = 0.5  # 预测因子，用于运动预测

# 追踪稳定性检测参数
STABLE_THRESHOLD = 8     # 稳定阈值：误差小于此值认为稳定
STABLE_COUNT_REQUIRED = 15  # 需要连续稳定的帧数
STABLE_COMMAND = b'\xAA'    # 稳定后发送的指令（16进制：0xAA）

# 矩形过滤参数
MIN_RECT_AREA = 100      # 最小矩形面积
MAX_RECT_AREA = 15000    # 最大矩形面积
MIN_ASPECT_RATIO = 0.2   # 最小长宽比（防止过于细长）
MAX_ASPECT_RATIO = 3.0   # 最大长宽比
MIN_WIDTH = 10           # 最小宽度
MIN_HEIGHT = 10          # 最小高度
MAX_AREA_RATIO = 0.3     # 最大面积占比（相对于图像总面积）
ENABLE_RECT_FILTER = True  # 是否启用矩形过滤（True=新过滤方法，False=原始方法）

# 性能优化参数
ENABLE_FAST_MODE = False   # 启用快速模式，减少延迟
SKIP_FRAME_COUNT = 0      # 跳帧数量（0=不跳帧，1=跳1帧）
REDUCE_DEBUG_OUTPUT = True # 减少调试输出以提高速度

# 白色色块检测参数（用于反推黑色边框）
WHITE_BLOB_THRESHOLD = [(57, 100, -128, 127, -128, 127)]  # 白色色块的LAB阈值
BLOB_X_STRIDE = 5         # X方向步长
BLOB_Y_STRIDE = 5         # Y方向步长
BLOB_PIXELS_THRESHOLD = 2000  # 最小像素数阈值
BLOB_MARGIN = True        # 是否包含边缘
MIN_SOLIDITY = 0.65       # 最小实心度（用于过滤形状）
MAX_SOLIDITY = 1.0        # 最大实心度
BORDER_THICKNESS = 5      # 推断的边框厚度（像素）

# 视角偏移补偿参数
ENABLE_PERSPECTIVE_CORRECTION = True  # 是否启用视角偏移补偿
PERSPECTIVE_CORRECTION_FACTOR = 0.3   # 补偿强度系数（0.1-0.5）
MIN_PERSPECTIVE_THRESHOLD = 5         # 最小视角偏移阈值（像素）
PERSPECTIVE_DEBUG = False             # 是否输出视角补偿调试信息

#起始舵机位置，参数
MOVE_STEP_TOP = 80 #切段的函数
start_x = 0.500
start_y = 0.500
START_POSITION = (start_x, start_y)
laser_x = 0
laser_y = 0
#---------------------------------------PID类定义------------------------------------------------#

try:

    class PID:
        def __init__(self, kp, ki, kd, input_value, target):
            self.e = 0              # 当前误差
            self.e_last = 0         # 上一次误差
            self.e_sum = 0          # 误差累积，用于积分项
            self.kp = kp            # 比例系数
            self.ki = ki            # 积分系数
            self.kd = kd            # 微分系数
            self.target = target    # 目标值
            self.input_value = input_value  # PWM占空比
            self.max_sum = 200      # 积分项最大值，防止积分饱和

        def cal(self, value):       # value观测值，识别到激光点的坐标
            self.e = self.target - value  # 目标值 - 观测值 = 误差

            # 积分项累加
            self.e_sum += self.e
            # 积分限幅，防止积分饱和
            if self.e_sum > self.max_sum:
                self.e_sum = self.max_sum
            elif self.e_sum < -self.max_sum:
                self.e_sum = -self.max_sum

            # 标准PID算法实现
            p_out = self.kp * self.e                      # 比例项
            i_out = self.ki * self.e_sum                  # 积分项
            d_out = self.kd * (self.e - self.e_last)      # 微分项

            delta = p_out + i_out + d_out

            # 保存本次误差
            self.e_last = self.e

            # 更新PWM占空比
            self.input_value = self.input_value + delta
            return self.input_value


#---------------------------------------舵机移动函数定义------------------------------------------------#

    class LaserController:
        def __init__(self, pid_class, pid_params, start_position, move_step_top=120):
            # 初始化参数
            self.PID_Kp, self.PID_Ki, self.PID_Kd = pid_params
            self.start_x, self.start_y = start_position
            self.move_step_top = move_step_top
            self.PID = pid_class

            # 状态变量
            self.counter = 0
            self.move_pencil_step_n = 0
            self.servo_pid_x = None
            self.servo_pid_y = None
            self.servo_static_count = 0
            self.is_stable = False
            self.PID_isSet = 0

        def line_int(self, corners):
            """线性插值函数"""
            if self.counter == 4:  # 最后一段（连接到第一点）
                star_idx, tar_idx = 4, 1
            else:
                star_idx, tar_idx = self.counter, (self.counter + 1)
    #             计算当前目标位置
            target_x = (corners[tar_idx][0] - corners[star_idx][0]) / self.move_step_top * self.move_pencil_step_n + corners[star_idx][0]
            target_y = (corners[tar_idx][1] - corners[star_idx][1]) / self.move_step_top * self.move_pencil_step_n + corners[star_idx][1]
    #             更新步进计数
            if self.is_stable:
                if self.move_pencil_step_n == self.move_step_top:
                    self.counter = (self.counter + 1)
                    if self.counter == 5:
                       self.counter = 1
                    self.move_pencil_step_n = 0
                else:
                    self.move_pencil_step_n += 1

            return target_x, target_y


        def servo_move(self, target_x, target_y, laser_x, laser_y):
            if self.PID_isSet == 0:
                self.servo_pid_x = self.PID(self.PID_Kp, self.PID_Ki, self.PID_Kd, self.start_x, target_x)
                self.servo_pid_y = self.PID(-self.PID_Kp, self.PID_Ki, self.PID_Kd, self.start_y, target_y)
                self.PID_isSet = 1
            if self.servo_pid_x.target != target_x:
                self.servo_pid_x.target = target_x
                self.servo_pid_x.e_sum = 0  # 重置积分项
            if self.servo_pid_y.target != target_y:
                self.servo_pid_y.target = target_y
                self.servo_pid_y.e_sum = 0  # 重置积分项

            # 计算新的占空比
            duty = self.servo_pid_x.cal(laser_x)
            duty = max(0.5, min(2.5, duty))  # 限制在0.5-2.5ms范围
            new_duty_x = round(duty * 1000, 2)  # 转换为500-2500范围

            duty = self.servo_pid_y.cal(laser_y)
            duty = max(0.5, min(2.5, duty))  # 限制在0.5-2.5ms范围
            new_duty_y = round(duty * 1000, 2)  # 转换为500-2500范围
            # 检查位置稳定性
            if abs(target_x - laser_x) <= 7 and abs(target_y - laser_y) <= 7:
                self.servo_static_count += 1
            else:
                self.servo_static_count = 0

            self.is_stable = self.servo_static_count >= 3
            #返回计算好的x,y轴PWM
            return new_duty_x, new_duty_y

    def filter_and_select_rect(rects):
        """
        改进的矩形过滤函数，提高识别准确度
        """
        if not rects:
            return None

        valid_rects = []

        for rect in rects:
            # 获取矩形参数
            x, y, w, h = rect.x(), rect.y(), rect.w(), rect.h()
            area = w * h

            # 1. 面积过滤：过滤太小和太大的矩形
            if area < MIN_RECT_AREA or area > MAX_RECT_AREA:
                continue

            # 2. 尺寸过滤：过滤太小的宽度和高度
            if w < MIN_WIDTH or h < MIN_HEIGHT:
                continue

            # 3. 长宽比过滤：过滤过于细长或过于扁平的矩形
            aspect_ratio = max(w, h) / min(w, h)
            if aspect_ratio < MIN_ASPECT_RATIO or aspect_ratio > MAX_ASPECT_RATIO:
                continue

            # 4. 相对面积过滤：过滤占图像面积过大的矩形（可能是背景）
            relative_area = area / (WIDTH * HEIGHT)
            if relative_area > MAX_AREA_RATIO:
                continue

            # 5. 位置过滤：过滤边缘位置的矩形（可选）
            margin = 10  # 边缘边距
            if x < margin or y < margin or (x + w) > (WIDTH - margin) or (y + h) > (HEIGHT - margin):
                continue

            # 6. 计算矩形质量分数
            # 综合考虑面积、形状因子、位置等
            shape_factor = 1.0 / aspect_ratio  # 越接近正方形分数越高
            center_x, center_y = x + w//2, y + h//2
            center_distance = ((center_x - WIDTH//2)**2 + (center_y - HEIGHT//2)**2)**0.5
            position_factor = 1.0 / (1.0 + center_distance / 100)  # 越靠近中心分数越高

            quality_score = area * shape_factor * position_factor

            valid_rects.append((rect, quality_score, area, aspect_ratio))

        if not valid_rects:
            return None

        # 返回质量分数最高的矩形
        best_rect_info = max(valid_rects, key=lambda x: x[1])
        best_rect, score, area, aspect_ratio = best_rect_info

        # 输出调试信息
        print(f"选择矩形: 面积({area}) 长宽比({aspect_ratio:.2f}) 质量分数({score:.1f})")

        return best_rect

    def detect_white_blobs_and_infer_border(img):
        """
        通过检测白色色块来反推黑色边框
        返回推断出的边框矩形信息
        """
        try:
            # 检测白色色块
            blobs = img.find_blobs(WHITE_BLOB_THRESHOLD,
                                 x_stride=BLOB_X_STRIDE,
                                 y_stride=BLOB_Y_STRIDE,
                                 pixels_threshold=BLOB_PIXELS_THRESHOLD,
                                 margin=BLOB_MARGIN)

            if not blobs:
                return None

            # 按面积排序，选择最大的色块
            blobs.sort(key=lambda b: b.area(), reverse=True)

            valid_blobs = []
            for blob in blobs:
                # 检查实心度（形状质量）
                if MIN_SOLIDITY < blob.solidity() <= MAX_SOLIDITY:
                    valid_blobs.append(blob)

            if not valid_blobs:
                return None

            # 选择最大的有效色块
            best_blob = valid_blobs[0]

            # 从白色色块推断黑色边框
            # 假设白色区域是黑色边框内部，边框有一定厚度
            border_thickness = BORDER_THICKNESS  # 使用可配置的边框厚度

            # 推断的黑色边框坐标（扩展白色区域）
            inferred_x = max(0, best_blob.x() - border_thickness)
            inferred_y = max(0, best_blob.y() - border_thickness)
            inferred_w = min(WIDTH - inferred_x, best_blob.w() + 2 * border_thickness)
            inferred_h = min(HEIGHT - inferred_y, best_blob.h() + 2 * border_thickness)

            # 创建一个兼容的矩形对象
            class InferredRect:
                def __init__(self, x, y, w, h, blob):
                    self._x = x
                    self._y = y
                    self._w = w
                    self._h = h
                    self._blob = blob  # 保存原始色块信息

                def x(self):
                    return self._x

                def y(self):
                    return self._y

                def w(self):
                    return self._w

                def h(self):
                    return self._h

                def rect(self):
                    return (self._x, self._y, self._w, self._h)

                def center(self):
                    """使用色块的中心作为更准确的中心点"""
                    return (self._blob.cx(), self._blob.cy())

                def blob(self):
                    """返回原始色块对象"""
                    return self._blob

            inferred_rect = InferredRect(inferred_x, inferred_y, inferred_w, inferred_h, best_blob)

            if not REDUCE_DEBUG_OUTPUT:
                print(f"检测到白色色块: 面积({best_blob.area()}) 实心度({best_blob.solidity():.2f})")
                print(f"推断边框: 位置({inferred_x},{inferred_y}) 尺寸({inferred_w}x{inferred_h})")

            return inferred_rect

        except Exception as e:
            print(f"白色色块检测出错: {e}")
            return None

    def calculate_perspective_correction(rect):
        """
        计算视角偏移补偿值
        通过分析矩形的几何特征判断视角方向并计算补偿量

        参数:
            rect: 检测到的矩形对象

        返回:
            correction_x: X轴补偿值（正值表示向右补偿，负值表示向左补偿）
        """
        if not ENABLE_PERSPECTIVE_CORRECTION:
            return 0

        try:
            # 获取矩形的基本信息
            x, y, w, h = rect.x(), rect.y(), rect.w(), rect.h()

            # 方法1: 通过矩形在图像中的位置判断视角偏移
            # 当矩形偏向图像左侧时，实际中心点应该向右偏移
            # 当矩形偏向图像右侧时，实际中心点应该向左偏移
            rect_center_x = x + w / 2
            image_center_x = WIDTH / 2
            position_offset = rect_center_x - image_center_x

            # 方法2: 通过长宽比分析视角偏移
            # 当视角偏左时，矩形会显得更窄（宽度压缩）
            # 当视角偏右时，矩形也会显得更窄
            aspect_ratio = w / h if h > 0 else 1.0

            # 假设正视时的理想长宽比为1.0（正方形）
            ideal_aspect_ratio = 1.0
            aspect_deviation = aspect_ratio - ideal_aspect_ratio

            # 方法3: 通过矩形的偏心程度计算补偿
            # 结合位置偏移和形状变化

            # 基于位置的补偿（主要方法）
            # 当矩形偏左时，真实中心应该向右偏移
            position_correction = -position_offset * PERSPECTIVE_CORRECTION_FACTOR

            # 基于形状的补偿（辅助方法）
            # 当矩形变窄时，根据位置判断补偿方向
            shape_correction = 0
            if abs(aspect_deviation) > 0.1:  # 形状明显变化时
                if position_offset < 0:  # 矩形在左侧且变窄
                    shape_correction = abs(aspect_deviation) * 10  # 向右补偿
                elif position_offset > 0:  # 矩形在右侧且变窄
                    shape_correction = -abs(aspect_deviation) * 10  # 向左补偿

            # 综合补偿值
            total_correction = position_correction + shape_correction

            # 限制补偿范围，避免过度补偿
            max_correction = w * 0.3  # 最大补偿不超过矩形宽度的30%
            total_correction = max(-max_correction, min(max_correction, total_correction))

            # 只有当补偿值超过最小阈值时才应用
            if abs(total_correction) < MIN_PERSPECTIVE_THRESHOLD:
                total_correction = 0

            if PERSPECTIVE_DEBUG:
                print(f"视角补偿分析:")
                print(f"  矩形位置: ({x}, {y}) 尺寸: {w}x{h}")
                print(f"  矩形中心: {rect_center_x:.1f}, 图像中心: {image_center_x}")
                print(f"  位置偏移: {position_offset:.1f}")
                print(f"  长宽比: {aspect_ratio:.2f} (理想: {ideal_aspect_ratio})")
                print(f"  位置补偿: {position_correction:.1f}")
                print(f"  形状补偿: {shape_correction:.1f}")
                print(f"  总补偿: {total_correction:.1f}")

            return total_correction

        except Exception as e:
            if PERSPECTIVE_DEBUG:
                print(f"视角补偿计算出错: {e}")
            return 0

    def calculate_advanced_perspective_correction(rect, img):
        """
        高级视角偏移补偿函数
        通过分析矩形边缘的像素分布来判断视角偏移

        参数:
            rect: 检测到的矩形对象
            img: 原始图像

        返回:
            correction_x: X轴补偿值
        """
        if not ENABLE_PERSPECTIVE_CORRECTION:
            return 0

        try:
            x, y, w, h = rect.x(), rect.y(), rect.w(), rect.h()

            # 方法：分析左右边缘的"厚度"差异
            # 当视角偏左时，左边缘会显得更厚（更模糊）
            # 当视角偏右时，右边缘会显得更厚

            # 定义边缘检测区域
            edge_width = max(3, min(w//10, 10))  # 边缘检测宽度

            # 左边缘区域
            left_x1 = max(0, x - edge_width)
            left_x2 = min(WIDTH, x + edge_width)
            left_y1 = max(0, y)
            left_y2 = min(HEIGHT, y + h)

            # 右边缘区域
            right_x1 = max(0, x + w - edge_width)
            right_x2 = min(WIDTH, x + w + edge_width)
            right_y1 = max(0, y)
            right_y2 = min(HEIGHT, y + h)

            # 分析边缘强度（简化版本，通过边缘位置判断）
            # 实际应用中可以通过图像梯度分析

            # 基于几何分析的补偿
            rect_center_x = x + w / 2
            image_center_x = WIDTH / 2

            # 计算矩形相对于图像中心的偏移
            offset_ratio = (rect_center_x - image_center_x) / (WIDTH / 2)

            # 基于偏移比例计算补偿
            # 当矩形偏左时（offset_ratio < 0），真实中心应该向右偏移
            # 当矩形偏右时（offset_ratio > 0），真实中心应该向左偏移
            base_correction = -offset_ratio * w * PERSPECTIVE_CORRECTION_FACTOR

            # 基于矩形形状的额外补偿
            aspect_ratio = w / h if h > 0 else 1.0

            # 当矩形变窄时（aspect_ratio < 1），增加补偿强度
            shape_factor = 1.0
            if aspect_ratio < 0.8:  # 矩形明显变窄
                shape_factor = 1.5
            elif aspect_ratio < 0.6:  # 矩形严重变窄
                shape_factor = 2.0

            total_correction = base_correction * shape_factor

            # 限制补偿范围
            max_correction = w * 0.4
            total_correction = max(-max_correction, min(max_correction, total_correction))

            # 应用最小阈值
            if abs(total_correction) < MIN_PERSPECTIVE_THRESHOLD:
                total_correction = 0

            if PERSPECTIVE_DEBUG:
                print(f"高级视角补偿:")
                print(f"  偏移比例: {offset_ratio:.3f}")
                print(f"  长宽比: {aspect_ratio:.3f}")
                print(f"  形状因子: {shape_factor:.2f}")
                print(f"  基础补偿: {base_correction:.1f}")
                print(f"  最终补偿: {total_correction:.1f}")

            return total_correction

        except Exception as e:
            if PERSPECTIVE_DEBUG:
                print(f"高级视角补偿计算出错: {e}")
            return 0

    def filter_and_select_rect_fast(rects):
        """
        快速矩形过滤函数，减少延迟
        只进行最基本的过滤，提高速度
        """
        if not rects:
            return None

        valid_rects = []

        for rect in rects:
            # 获取矩形参数
            x, y, w, h = rect.x(), rect.y(), rect.w(), rect.h()
            area = w * h

            # 只进行最基本的过滤
            # 1. 面积过滤：过滤太小的矩形
            if area < MIN_RECT_AREA:
                continue

            # 2. 简单的长宽比过滤
            aspect_ratio = max(w, h) / min(w, h)
            if aspect_ratio > MAX_ASPECT_RATIO:
                continue

            # 3. 简单的质量评分（只考虑面积）
            valid_rects.append((rect, area))

        if not valid_rects:
            return None

        # 返回面积最大的矩形（快速选择）
        best_rect = max(valid_rects, key=lambda x: x[1])[0]
        return best_rect

    # 保留原来的max_rect函数作为备用
    def max_rect(rects):
        """原始的最大面积矩形选择函数（备用）"""
        max_rect = None
        max_size = 0
        for rect in rects:
            if (rect[2]*rect[3])>max_size:
                max_rect=rect
                max_size=rect[2]*rect[3]
        return max_rect

#--------------------------------------串口对象初始化-------------------------------------------------#

    uart = YbUart(baudrate=115200)
    # laser_ctrl = LaserController(pid_class=PID, pid_params=PID_PARAMS, start_position=START_POSITION, move_step_top=MOVE_STEP_TOP)

    # PD控制器变量初始化
    error_last = 0  # 上一次误差，用于计算微分项

    # 追踪稳定性检测变量
    stable_count = 0        # 连续稳定的帧数计数
    is_tracking_stable = False  # 追踪是否稳定
    stable_command_sent = False # 稳定指令是否已发送

    # 性能优化变量
    frame_count = 0         # 帧计数器，用于跳帧
    last_valid_rect = None  # 上一次有效的矩形，用于预测

#--------------------------------------FPIOA端口定义-------------------------------------------------#

    fpioa = FPIOA()#初始化FPIOA对象
    #设置按键
    fpioa.set_function(61, FPIOA.GPIO61)#确定该端口的功能为GPIO
    key = Pin(61, Pin.IN, Pin.PULL_DOWN)#设置fpioa61端口的模式，创建该端口对象

    #设置UART3_TXD
    fpioa.set_function(33, FPIOA.UART3_RXD, ie=1, oe=0)
    pin33 = Pin(33,Pin.OUT)#设置fpioa32端口的模式，创建该端口的对象
    #设置UART3_RXD
    fpioa.set_function(32, FPIOA.UART3_TXD, ie=0, oe=1)
    pin32 = Pin(32,Pin.OUT)#设置fpioa32端口的模式，创建该端口的对象

#    uart3 = UART(UART.UART3, 115200)
#    uart3.write('a%04d%04db' % (start_x * 100, start_y * 100))
    # uart.send('a%04d%04db' % (start_x * 1000, start_y * 1000))

#    aa = ThresholdSetter(red1, uart3)

#------------------------------------传感器配置初始化-----------------------------------------------#

    sensor = Sensor()   #初始化传感器变量
    sensor.reset()      #传感器重置
    sensor.set_framesize(width = WIDTH, height = HEIGHT, chn = CAM_CHN_ID_0)#设置通道0的输出尺寸
    sensor.set_pixformat(Sensor.RGB565,chn = CAM_CHN_ID_0)#设置通道0的输出格式为RGB565
    Display.init(Display.ST7701, width = 640, height = 480, to_ide = True)
    MediaManager.init()#初始化媒体管理器
    sensor.run()#开启传感器
    fps = time.clock()#创建时钟对象用于计算帧率


#---------------------------------------每一帧图像处理----------------------------------------------#

    while True:
        fps.tick()#帧率计时器tick
        os.exitpoint()#检查是否退出程序

        # 跳帧处理以提高响应速度
        frame_count += 1
        if SKIP_FRAME_COUNT > 0 and (frame_count % (SKIP_FRAME_COUNT + 1)) != 0:
            continue

        img = sensor.snapshot(chn = CAM_CHN_ID_0)#读取通道0的每一张图像
#        img = img.copy(roi = roi)#图片裁剪

        # 不再使用二值化，直接在彩色图像上检测白色色块
        # img = img.binary([(0, 30)])  # 注释掉二值化处理

#----------------------------------------白色色块检测实现（反推黑色边框）-----------------------------------------------#

        # 使用白色色块检测来反推黑色边框
        rect = detect_white_blobs_and_infer_border(img)

        if rect:  # 检测到有效的推断边框
            if not REDUCE_DEBUG_OUTPUT:
                print("使用白色色块反推边框方法")

            # 绘制检测结果
            if hasattr(rect, 'blob'):
                # 绘制原始白色色块（绿色）
                blob = rect.blob()
                img.draw_rectangle(blob.rect(), color=(0, 255, 0))  # 绿色框显示白色色块
                img.draw_cross((blob.cx(), blob.cy()), color=(0, 255, 0))  # 绿色十字显示色块中心

                # 绘制推断的黑色边框（红色）
                img.draw_rectangle(rect.rect(), color=(255, 0, 0))  # 红色框显示推断边框

                # 使用色块中心作为控制点
                original_x, original_y = rect.center()  # 使用色块的精确中心

                # 应用视角偏移补偿（可选择使用基础或高级补偿）
                # correction_x = calculate_perspective_correction(rect)  # 基础补偿
                correction_x = calculate_advanced_perspective_correction(rect, img)  # 高级补偿
                local_x = original_x + correction_x
                local_y = original_y  # Y轴不需要补偿

                # 绘制原始中心点（绿色）和补偿后中心点（红色）
                img.draw_cross((original_x, original_y), color=(0, 255, 0))  # 绿色十字显示原始中心
                img.draw_cross((local_x, local_y), color=(255, 0, 0))  # 红色十字显示补偿后中心

                # 如果有补偿，绘制补偿向量
                if abs(correction_x) > MIN_PERSPECTIVE_THRESHOLD:
                    img.draw_line((original_x, original_y, local_x, local_y), color=(255, 255, 0))  # 黄色线显示补偿向量

                # 显示检测信息
                if not REDUCE_DEBUG_OUTPUT:
                    w, h = rect.w(), rect.h()
                    area = blob.area()
                    solidity = blob.solidity()
                    print(f"白色色块: 中心({blob.cx()},{blob.cy()}) 面积({area}) 实心度({solidity:.2f})")
                    print(f"推断边框: 位置({rect.x()},{rect.y()}) 尺寸({w}x{h})")
            else:
                # 如果是普通矩形（备用方案）
                img.draw_rectangle(rect.rect(), color=(255, 0, 0))
                original_x, original_y = (rect.x() + int(rect.w() / 2), rect.y() + int(rect.h() / 2))

                # 应用视角偏移补偿（可选择使用基础或高级补偿）
                # correction_x = calculate_perspective_correction(rect)  # 基础补偿
                correction_x = calculate_advanced_perspective_correction(rect, img)  # 高级补偿
                local_x = original_x + correction_x
                local_y = original_y  # Y轴不需要补偿

                # 绘制原始中心点（绿色）和补偿后中心点（红色）
                img.draw_cross((original_x, original_y), color=(0, 255, 0))  # 绿色十字显示原始中心
                img.draw_cross((local_x, local_y), color=(255, 0, 0))  # 红色十字显示补偿后中心

                # 如果有补偿，绘制补偿向量
                if abs(correction_x) > MIN_PERSPECTIVE_THRESHOLD:
                    img.draw_line((original_x, original_y, local_x, local_y), color=(255, 255, 0))  # 黄色线显示补偿向量

            if rect:  # 确保有有效矩形才执行控制逻辑
                # --- PD控制逻辑 ---
                # 1. 计算与屏幕中心的误差
                error_x = local_x - (WIDTH / 2)

                # 2. 应用中心死区
                if abs(error_x) > DEAD_ZONE:
                    # 3. 计算误差变化率（微分项）
                    error_derivative = error_x - error_last

                    # 4. 运动预测（减少延迟）
                    predicted_error = error_x + error_derivative * PREDICTION_FACTOR

                    # 5. PD控制器计算输出（使用预测误差）
                    p_output = predicted_error * P_GAIN      # 比例项：预测误差
                    d_output = error_derivative * D_GAIN     # 微分项：误差变化趋势
                    pd_output = p_output + d_output          # PD控制器总输出

                    # 5. 转换为步数
                    steps_to_move = int(pd_output)

                    # 6. 构建并发送指令
                    # 限制最大步数，防止失控
                    steps_to_move = max(-9999, min(9999, steps_to_move))

                    sign = '+' if steps_to_move >= 0 else '-'
                    command = "s%c%04db" % (sign, abs(steps_to_move))

                    uart.send(command)

                    # 减少调试输出以提高速度
                    if not REDUCE_DEBUG_OUTPUT:
                        print(f"Error: {error_x:.1f}, Derivative: {error_derivative:.1f}, P: {p_output:.1f}, D: {d_output:.1f}, Steps: {steps_to_move}")

                    # 7. 更新上一次误差
                    error_last = error_x

                    # 重置稳定计数（因为还在调整）
                    stable_count = 0
                    is_tracking_stable = False
                    stable_command_sent = False
                else:
                    # 在死区内时，也要更新上一次误差，保持微分项的连续性
                    error_last = error_x
                    print(f"In dead zone, error: {error_x:.1f}")
                # --- 追踪稳定性检测 ---
                # 检查是否在稳定阈值内
                if abs(error_x) <= STABLE_THRESHOLD:
                    stable_count += 1
                    if stable_count >= STABLE_COUNT_REQUIRED:
                        if not is_tracking_stable:
                            is_tracking_stable = True
                            print(f"🎯 追踪已稳定！连续{stable_count}帧误差小于{STABLE_THRESHOLD}像素")

                        # 发送稳定指令（只发送一次）
                        if not stable_command_sent:
                            stable_command_sent = True
                            uart.send(STABLE_COMMAND)
                            print(f"📡 发送稳定指令: 0x{STABLE_COMMAND.hex().upper()}")
                else:
                    # 误差超出稳定阈值，重置计数
                    if stable_count > 0:
                        print(f"⚠️  追踪不稳定，重置计数（误差: {error_x:.1f}）")
                    stable_count = 0
                    is_tracking_stable = False
                    stable_command_sent = False
        else:
            # 未检测到白色色块时，重置稳定性状态和微分项
            if stable_count > 0:
                print("❌ 未检测到白色色块，重置稳定状态")
            stable_count = 0
            is_tracking_stable = False
            stable_command_sent = False
            error_last = 0  # 重置上一次误差

        img = img.crop(x_scale = 320 / 160, y_scale = 240 / 120)
#        img.draw_string_advanced(50, 50, 80, "fps: {}".format(fps.fps()), color = (255, 0, 0))#打印帧率
#        img.compressed_for_ide()#传输图片到ide
        Display.show_image(img)#显示在屏幕上
        gc.collect()#执行垃圾回收
#        time.sleep_ms(5)

#------------------------------------异常捕获+退出数据清理-------------------------------------------#

except KeyboardInterrupt as e:# 捕获键盘中断异常（用户手动停止）
    print("用户停止: ", e)
except BaseException as e:# 捕获所有其他异常
    print(f"异常: '{e}'")
finally:#无论如何都执行清理工作
    if 'sensor' in globals() and isinstance(sensor, Sensor):# 停止传感器运行（如果传感器对象存在）
        sensor.stop()
    # 反初始化显示模块，串口模块
    Display.deinit()
    uart.deinit()
    # 设置退出点，允许进入睡眠模式
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    # 释放媒体缓冲区
    MediaManager.deinit()
