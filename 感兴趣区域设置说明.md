# 感兴趣区域（ROI）设置说明

## 功能概述

为白色色块检测添加了感兴趣区域（Region of Interest, ROI）功能，将检测范围限制在画面向内缩进30像素的区域内，避免边缘干扰，提高检测精度。

## 配置参数

### 新增参数
```python
# 感兴趣区域参数
ROI_MARGIN = 30           # 感兴趣区域边距（向内缩进像素数）
# 计算感兴趣区域：(x, y, w, h)
BLOB_ROI = (ROI_MARGIN, ROI_MARGIN, WIDTH - 2 * ROI_MARGIN, HEIGHT - 2 * ROI_MARGIN)

# 可视化参数
SHOW_ROI_BORDER = True    # 是否显示感兴趣区域边框
```

### 区域计算
- **原始画面**: 320 × 240 像素
- **感兴趣区域**: 260 × 180 像素
- **区域坐标**: (30, 30, 260, 180)
- **边距**: 上下左右各30像素

## 实现细节

### 1. ROI区域定义
```python
# 感兴趣区域计算公式
x = ROI_MARGIN                    # 左边距：30
y = ROI_MARGIN                    # 上边距：30  
w = WIDTH - 2 * ROI_MARGIN        # 宽度：320 - 60 = 260
h = HEIGHT - 2 * ROI_MARGIN       # 高度：240 - 60 = 180
```

### 2. 检测函数修改
```python
# 修改前
blobs = img.find_blobs(WHITE_BLOB_THRESHOLD,
                     x_stride=BLOB_X_STRIDE,
                     y_stride=BLOB_Y_STRIDE,
                     pixels_threshold=BLOB_PIXELS_THRESHOLD,
                     margin=BLOB_MARGIN)

# 修改后（添加ROI参数）
blobs = img.find_blobs(WHITE_BLOB_THRESHOLD,
                     roi=BLOB_ROI,
                     x_stride=BLOB_X_STRIDE,
                     y_stride=BLOB_Y_STRIDE,
                     pixels_threshold=BLOB_PIXELS_THRESHOLD,
                     margin=BLOB_MARGIN)
```

### 3. 可视化显示
```python
# 绘制感兴趣区域边框
if SHOW_ROI_BORDER:
    roi_x, roi_y, roi_w, roi_h = BLOB_ROI
    img.draw_rectangle((roi_x, roi_y, roi_w, roi_h), color=(0, 0, 255))  # 蓝色边框
```

## 优势和效果

### 1. 减少边缘干扰
- 排除画面边缘的噪声和干扰
- 避免检测到不完整的目标对象
- 提高检测的稳定性

### 2. 提高检测精度
- 专注于画面中心区域的目标
- 减少误检和漏检
- 提升整体检测质量

### 3. 性能优化
- 减少需要处理的像素数量
- 提高检测速度
- 降低计算负担

## 视觉反馈

运行程序时您会看到：
- **蓝色矩形框**: 显示感兴趣区域的边界
- **绿色框**: 检测到的白色色块
- **红色框**: 推断的黑色边框
- **绿色十字**: 原始中心点
- **红色十字**: 补偿后中心点
- **黄色线**: 视角补偿向量

## 参数调整建议

### 1. 边距调整
```python
# 根据实际需要调整边距
ROI_MARGIN = 20    # 较小边距，检测区域更大
ROI_MARGIN = 40    # 较大边距，检测区域更小
ROI_MARGIN = 50    # 更大边距，专注中心区域
```

### 2. 动态ROI（高级功能）
可以根据检测结果动态调整ROI：
```python
# 示例：根据上一帧检测结果调整ROI
if last_detection_center:
    # 以上次检测中心为基础设置ROI
    dynamic_roi = calculate_dynamic_roi(last_detection_center)
```

### 3. 多ROI检测（扩展功能）
可以设置多个感兴趣区域：
```python
# 示例：设置多个ROI区域
ROI_LIST = [
    (30, 30, 260, 180),    # 主要区域
    (50, 50, 220, 140),    # 中心区域
]
```

## 注意事项

### 1. 坐标系统
- ROI内检测到的坐标是相对于整个图像的绝对坐标
- 不需要额外的坐标转换
- 视角补偿算法正常工作

### 2. 边距设置
- 边距过小：可能仍有边缘干扰
- 边距过大：可能错过有效目标
- 建议根据实际应用场景调整

### 3. 性能影响
- ROI设置对性能影响很小
- 主要优化在于减少处理像素数
- 不会影响检测精度

## 故障排除

### 1. 检测不到目标
- 检查目标是否在ROI区域内
- 适当减小 `ROI_MARGIN` 值
- 确认白色阈值设置正确

### 2. ROI边框不显示
- 检查 `SHOW_ROI_BORDER` 是否为 True
- 确认蓝色边框颜色设置正确
- 检查绘制代码是否正确执行

### 3. 检测精度下降
- 可能ROI设置过小，遗漏了目标
- 尝试调整边距大小
- 检查目标是否经常出现在边缘区域

## 扩展功能建议

### 1. 自适应ROI
根据检测历史动态调整ROI大小和位置

### 2. 多级ROI
设置粗检测和精检测两级ROI

### 3. ROI形状优化
使用圆形或椭圆形ROI替代矩形ROI

### 4. 智能边距
根据图像内容自动计算最佳边距
