# 视角偏移补偿测试脚本
# 用于测试和验证视角补偿算法的效果

import math

# 模拟参数
WIDTH = 320
HEIGHT = 240

# 视角偏移补偿参数
ENABLE_PERSPECTIVE_CORRECTION = True
PERSPECTIVE_CORRECTION_FACTOR = 0.3
MIN_PERSPECTIVE_THRESHOLD = 5
PERSPECTIVE_DEBUG = True

def calculate_perspective_correction(rect_info):
    """
    计算视角偏移补偿值（测试版本）
    
    参数:
        rect_info: 字典，包含 {'x': x, 'y': y, 'w': w, 'h': h}
        
    返回:
        correction_x: X轴补偿值
    """
    if not ENABLE_PERSPECTIVE_CORRECTION:
        return 0
        
    try:
        x, y, w, h = rect_info['x'], rect_info['y'], rect_info['w'], rect_info['h']
        
        # 方法1: 通过矩形在图像中的位置判断视角偏移
        rect_center_x = x + w / 2
        image_center_x = WIDTH / 2
        position_offset = rect_center_x - image_center_x
        
        # 方法2: 通过长宽比分析视角偏移
        aspect_ratio = w / h if h > 0 else 1.0
        ideal_aspect_ratio = 1.0
        aspect_deviation = aspect_ratio - ideal_aspect_ratio
        
        # 基于位置的补偿（主要方法）
        position_correction = -position_offset * PERSPECTIVE_CORRECTION_FACTOR
        
        # 基于形状的补偿（辅助方法）
        shape_correction = 0
        if abs(aspect_deviation) > 0.1:
            if position_offset < 0:  # 矩形在左侧且变窄
                shape_correction = abs(aspect_deviation) * 10
            elif position_offset > 0:  # 矩形在右侧且变窄
                shape_correction = -abs(aspect_deviation) * 10
        
        # 综合补偿值
        total_correction = position_correction + shape_correction
        
        # 限制补偿范围
        max_correction = w * 0.3
        total_correction = max(-max_correction, min(max_correction, total_correction))
        
        # 应用最小阈值
        if abs(total_correction) < MIN_PERSPECTIVE_THRESHOLD:
            total_correction = 0
        
        if PERSPECTIVE_DEBUG:
            print(f"视角补偿分析:")
            print(f"  矩形位置: ({x}, {y}) 尺寸: {w}x{h}")
            print(f"  矩形中心: {rect_center_x:.1f}, 图像中心: {image_center_x}")
            print(f"  位置偏移: {position_offset:.1f}")
            print(f"  长宽比: {aspect_ratio:.2f} (理想: {ideal_aspect_ratio})")
            print(f"  位置补偿: {position_correction:.1f}")
            print(f"  形状补偿: {shape_correction:.1f}")
            print(f"  总补偿: {total_correction:.1f}")
        
        return total_correction
        
    except Exception as e:
        if PERSPECTIVE_DEBUG:
            print(f"视角补偿计算出错: {e}")
        return 0

def calculate_advanced_perspective_correction(rect_info):
    """
    高级视角偏移补偿函数（测试版本）
    """
    if not ENABLE_PERSPECTIVE_CORRECTION:
        return 0
        
    try:
        x, y, w, h = rect_info['x'], rect_info['y'], rect_info['w'], rect_info['h']
        
        # 基于几何分析的补偿
        rect_center_x = x + w / 2
        image_center_x = WIDTH / 2
        
        # 计算矩形相对于图像中心的偏移
        offset_ratio = (rect_center_x - image_center_x) / (WIDTH / 2)
        
        # 基于偏移比例计算补偿
        base_correction = -offset_ratio * w * PERSPECTIVE_CORRECTION_FACTOR
        
        # 基于矩形形状的额外补偿
        aspect_ratio = w / h if h > 0 else 1.0
        
        # 当矩形变窄时，增加补偿强度
        shape_factor = 1.0
        if aspect_ratio < 0.8:
            shape_factor = 1.5
        elif aspect_ratio < 0.6:
            shape_factor = 2.0
            
        total_correction = base_correction * shape_factor
        
        # 限制补偿范围
        max_correction = w * 0.4
        total_correction = max(-max_correction, min(max_correction, total_correction))
        
        # 应用最小阈值
        if abs(total_correction) < MIN_PERSPECTIVE_THRESHOLD:
            total_correction = 0
            
        if PERSPECTIVE_DEBUG:
            print(f"高级视角补偿:")
            print(f"  偏移比例: {offset_ratio:.3f}")
            print(f"  长宽比: {aspect_ratio:.3f}")
            print(f"  形状因子: {shape_factor:.2f}")
            print(f"  基础补偿: {base_correction:.1f}")
            print(f"  最终补偿: {total_correction:.1f}")
            
        return total_correction
        
    except Exception as e:
        if PERSPECTIVE_DEBUG:
            print(f"高级视角补偿计算出错: {e}")
        return 0

def test_perspective_correction():
    """测试视角补偿功能"""
    print("=" * 50)
    print("视角偏移补偿测试")
    print("=" * 50)
    
    # 测试用例：不同位置和形状的矩形
    test_cases = [
        {"name": "中心正方形", "x": 140, "y": 100, "w": 40, "h": 40},
        {"name": "左侧矩形", "x": 50, "y": 100, "w": 40, "h": 40},
        {"name": "右侧矩形", "x": 230, "y": 100, "w": 40, "h": 40},
        {"name": "左侧窄矩形", "x": 50, "y": 100, "w": 25, "h": 40},
        {"name": "右侧窄矩形", "x": 230, "y": 100, "w": 25, "h": 40},
        {"name": "极左窄矩形", "x": 20, "y": 100, "w": 20, "h": 40},
        {"name": "极右窄矩形", "x": 280, "y": 100, "w": 20, "h": 40},
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {case['name']}")
        print("-" * 30)
        
        rect_info = {k: v for k, v in case.items() if k != 'name'}
        
        # 计算原始中心点
        original_x = rect_info['x'] + rect_info['w'] / 2
        
        # 基础补偿
        print("基础补偿算法:")
        correction_basic = calculate_perspective_correction(rect_info)
        corrected_x_basic = original_x + correction_basic
        
        print(f"原始中心: {original_x:.1f}")
        print(f"补偿值: {correction_basic:.1f}")
        print(f"补偿后中心: {corrected_x_basic:.1f}")
        
        print("\n高级补偿算法:")
        correction_advanced = calculate_advanced_perspective_correction(rect_info)
        corrected_x_advanced = original_x + correction_advanced
        
        print(f"补偿值: {correction_advanced:.1f}")
        print(f"补偿后中心: {corrected_x_advanced:.1f}")
        
        # 分析补偿效果
        center_error_original = abs(original_x - WIDTH/2)
        center_error_basic = abs(corrected_x_basic - WIDTH/2)
        center_error_advanced = abs(corrected_x_advanced - WIDTH/2)
        
        print(f"\n误差分析:")
        print(f"原始误差: {center_error_original:.1f}")
        print(f"基础补偿后误差: {center_error_basic:.1f}")
        print(f"高级补偿后误差: {center_error_advanced:.1f}")
        
        if center_error_basic < center_error_original:
            print("✓ 基础补偿有效")
        else:
            print("✗ 基础补偿无效")
            
        if center_error_advanced < center_error_original:
            print("✓ 高级补偿有效")
        else:
            print("✗ 高级补偿无效")

def test_different_parameters():
    """测试不同参数设置的效果"""
    print("\n" + "=" * 50)
    print("参数敏感性测试")
    print("=" * 50)
    
    # 固定测试用例
    test_rect = {"x": 50, "y": 100, "w": 25, "h": 40}  # 左侧窄矩形
    original_x = test_rect['x'] + test_rect['w'] / 2
    
    # 测试不同的补偿因子
    factors = [0.1, 0.2, 0.3, 0.4, 0.5]
    
    print(f"测试矩形: 位置({test_rect['x']}, {test_rect['y']}) 尺寸({test_rect['w']}x{test_rect['h']})")
    print(f"原始中心: {original_x:.1f}")
    print(f"图像中心: {WIDTH/2}")
    print(f"原始误差: {abs(original_x - WIDTH/2):.1f}")
    print()
    
    global PERSPECTIVE_CORRECTION_FACTOR, PERSPECTIVE_DEBUG
    PERSPECTIVE_DEBUG = False  # 关闭详细调试信息
    
    print("补偿因子测试:")
    print("因子\t补偿值\t补偿后中心\t误差\t改善")
    print("-" * 50)
    
    for factor in factors:
        PERSPECTIVE_CORRECTION_FACTOR = factor
        correction = calculate_advanced_perspective_correction(test_rect)
        corrected_x = original_x + correction
        error = abs(corrected_x - WIDTH/2)
        improvement = abs(original_x - WIDTH/2) - error
        
        print(f"{factor:.1f}\t{correction:.1f}\t{corrected_x:.1f}\t\t{error:.1f}\t{improvement:+.1f}")

if __name__ == "__main__":
    test_perspective_correction()
    test_different_parameters()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    print("使用建议:")
    print("1. 根据测试结果调整 PERSPECTIVE_CORRECTION_FACTOR")
    print("2. 观察不同位置矩形的补偿效果")
    print("3. 选择最适合您应用场景的补偿算法")
    print("4. 在实际设备上进行验证测试")
