# 视角偏移补偿功能使用说明

## 功能概述

本功能通过分析矩形的几何特征来判断视角偏移方向，并计算相应的补偿值来修正中心点坐标。主要解决当摄像头视角偏左或偏右时，检测到的矩形中心点与实际中心点存在误差的问题。

## 核心原理

### 1. 基础补偿算法
- **位置分析**: 通过矩形在图像中的位置判断视角偏移
  - 矩形偏左 → 真实中心应向右偏移
  - 矩形偏右 → 真实中心应向左偏移
- **形状分析**: 通过长宽比变化辅助判断
  - 视角偏移时矩形会变窄
  - 结合位置信息确定补偿方向

### 2. 高级补偿算法
- **偏移比例计算**: 基于矩形相对图像中心的偏移比例
- **形状因子**: 根据长宽比变化动态调整补偿强度
- **自适应补偿**: 矩形越窄，补偿强度越大

## 配置参数

### 主要参数
```python
ENABLE_PERSPECTIVE_CORRECTION = True    # 是否启用视角补偿
PERSPECTIVE_CORRECTION_FACTOR = 0.3     # 补偿强度系数 (0.1-0.5)
MIN_PERSPECTIVE_THRESHOLD = 5           # 最小补偿阈值 (像素)
PERSPECTIVE_DEBUG = True                # 调试信息输出
```

### 预设模式
- **conservative**: 保守模式，补偿较小，适合初次测试
- **moderate**: 中等模式，平衡的补偿强度（推荐）
- **aggressive**: 激进模式，较强补偿，适合严重偏移

## 使用方法

### 1. 基本使用
```python
# 在主程序中导入配置
from perspective_correction_config import *

# 在检测到矩形后应用补偿
original_x, original_y = rect.center()
correction_x = calculate_advanced_perspective_correction(rect, img)
corrected_x = original_x + correction_x
```

### 2. 参数调试
1. 首先使用 `conservative` 预设测试
2. 观察绿色十字（原始中心）和红色十字（补偿后中心）
3. 根据效果调整到 `moderate` 或 `aggressive`
4. 必要时手动调整 `PERSPECTIVE_CORRECTION_FACTOR`

### 3. 视觉反馈
- **绿色十字**: 原始检测中心点
- **红色十字**: 补偿后中心点
- **黄色线**: 补偿向量（显示补偿方向和大小）

## 测试结果分析

根据测试脚本的结果：

### 效果验证
- **左侧矩形**: 原始误差90.0 → 补偿后78.0 (改善13.3%)
- **右侧矩形**: 原始误差90.0 → 补偿后78.0 (改善13.3%)
- **左侧窄矩形**: 原始误差97.5 → 补偿后90.0 (改善7.7%)
- **右侧窄矩形**: 原始误差82.5 → 补偿后75.0 (改善9.1%)

### 补偿强度对比
| 补偿因子 | 补偿值 | 误差改善 |
|---------|--------|----------|
| 0.1     | 0.0    | 0.0      |
| 0.2     | 0.0    | 0.0      |
| 0.3     | 6.9    | 6.9      |
| 0.4     | 9.1    | 9.1      |
| 0.5     | 10.0   | 10.0     |

## 实际应用建议

### 1. 初始设置
```python
# 推荐的初始配置
ENABLE_PERSPECTIVE_CORRECTION = True
PERSPECTIVE_CORRECTION_FACTOR = 0.3
MIN_PERSPECTIVE_THRESHOLD = 5
PERSPECTIVE_DEBUG = True  # 调试阶段开启
```

### 2. 优化流程
1. **测试不同视角**: 在偏左、正中、偏右位置测试
2. **观察补偿效果**: 检查补偿是否过度或不足
3. **调整参数**: 根据实际效果微调补偿因子
4. **验证稳定性**: 确保在各种条件下都有良好表现

### 3. 性能考虑
- 补偿计算开销很小，不会影响实时性
- 可以根据需要选择基础或高级补偿算法
- 调试模式会增加输出，生产环境建议关闭

## 故障排除

### 常见问题
1. **补偿无效果**
   - 检查 `ENABLE_PERSPECTIVE_CORRECTION` 是否为 True
   - 确认补偿值是否超过 `MIN_PERSPECTIVE_THRESHOLD`
   - 尝试增大 `PERSPECTIVE_CORRECTION_FACTOR`

2. **补偿过度**
   - 减小 `PERSPECTIVE_CORRECTION_FACTOR`
   - 检查 `MAX_CORRECTION_RATIO` 设置
   - 考虑使用更保守的预设

3. **补偿方向错误**
   - 检查矩形检测是否准确
   - 验证坐标系统是否正确
   - 查看调试信息确认计算逻辑

### 调试技巧
- 开启 `PERSPECTIVE_DEBUG` 查看详细计算过程
- 使用测试脚本验证算法逻辑
- 观察视觉反馈确认补偿效果
- 记录不同场景下的最佳参数

## 扩展功能

### 未来改进方向
1. **自适应参数**: 根据矩形大小和位置动态调整参数
2. **历史数据**: 利用多帧数据提高补偿精度
3. **机器学习**: 训练模型自动学习最佳补偿策略
4. **Y轴补偿**: 扩展到垂直方向的视角补偿

### 自定义扩展
可以根据具体应用场景修改补偿算法：
- 添加更多几何特征分析
- 结合图像梯度信息
- 考虑镜头畸变校正
- 集成IMU数据进行姿态补偿
