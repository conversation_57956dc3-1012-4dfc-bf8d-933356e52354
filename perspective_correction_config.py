# 视角偏移补偿配置文件
# 用于调试和优化视角补偿参数

# 基础补偿参数
ENABLE_PERSPECTIVE_CORRECTION = True  # 是否启用视角偏移补偿
PERSPECTIVE_CORRECTION_FACTOR = 0.3   # 补偿强度系数（0.1-0.5，建议从0.2开始调试）
MIN_PERSPECTIVE_THRESHOLD = 5         # 最小视角偏移阈值（像素）
PERSPECTIVE_DEBUG = True              # 是否输出视角补偿调试信息

# 高级补偿参数
USE_ADVANCED_CORRECTION = True        # 是否使用高级补偿算法
SHAPE_FACTOR_THRESHOLD_1 = 0.8        # 形状因子阈值1（矩形变窄判断）
SHAPE_FACTOR_THRESHOLD_2 = 0.6        # 形状因子阈值2（矩形严重变窄判断）
SHAPE_FACTOR_MULTIPLIER_1 = 1.5       # 形状因子乘数1
SHAPE_FACTOR_MULTIPLIER_2 = 2.0       # 形状因子乘数2
MAX_CORRECTION_RATIO = 0.4            # 最大补偿比例（相对于矩形宽度）

# 测试模式配置
TEST_MODE = False                     # 测试模式（显示更多调试信息）
SHOW_CORRECTION_VECTOR = True         # 是否显示补偿向量
SHOW_ORIGINAL_CENTER = True           # 是否显示原始中心点

# 不同场景的预设参数
PRESETS = {
    "conservative": {  # 保守模式：补偿较小，适合初次测试
        "PERSPECTIVE_CORRECTION_FACTOR": 0.2,
        "MIN_PERSPECTIVE_THRESHOLD": 8,
        "MAX_CORRECTION_RATIO": 0.3
    },
    "moderate": {      # 中等模式：平衡的补偿强度
        "PERSPECTIVE_CORRECTION_FACTOR": 0.3,
        "MIN_PERSPECTIVE_THRESHOLD": 5,
        "MAX_CORRECTION_RATIO": 0.4
    },
    "aggressive": {    # 激进模式：较强的补偿，适合严重偏移
        "PERSPECTIVE_CORRECTION_FACTOR": 0.5,
        "MIN_PERSPECTIVE_THRESHOLD": 3,
        "MAX_CORRECTION_RATIO": 0.5
    }
}

# 当前使用的预设（可选择：conservative, moderate, aggressive, 或 None 使用自定义参数）
CURRENT_PRESET = "moderate"

def apply_preset(preset_name):
    """应用预设参数"""
    if preset_name in PRESETS:
        preset = PRESETS[preset_name]
        globals().update(preset)
        print(f"已应用预设: {preset_name}")
        for key, value in preset.items():
            print(f"  {key}: {value}")
    else:
        print(f"未找到预设: {preset_name}")

def get_current_config():
    """获取当前配置"""
    config = {
        "ENABLE_PERSPECTIVE_CORRECTION": ENABLE_PERSPECTIVE_CORRECTION,
        "PERSPECTIVE_CORRECTION_FACTOR": PERSPECTIVE_CORRECTION_FACTOR,
        "MIN_PERSPECTIVE_THRESHOLD": MIN_PERSPECTIVE_THRESHOLD,
        "USE_ADVANCED_CORRECTION": USE_ADVANCED_CORRECTION,
        "MAX_CORRECTION_RATIO": MAX_CORRECTION_RATIO,
        "PERSPECTIVE_DEBUG": PERSPECTIVE_DEBUG
    }
    return config

def print_current_config():
    """打印当前配置"""
    config = get_current_config()
    print("当前视角补偿配置:")
    for key, value in config.items():
        print(f"  {key}: {value}")

# 自动应用预设
if CURRENT_PRESET:
    apply_preset(CURRENT_PRESET)

# 使用说明
USAGE_INSTRUCTIONS = """
视角偏移补偿使用说明:

1. 参数调试建议:
   - 首先使用 "conservative" 预设进行测试
   - 观察补偿效果，如果补偿不足，切换到 "moderate"
   - 如果仍然不足，使用 "aggressive" 或手动调整参数

2. 关键参数说明:
   - PERSPECTIVE_CORRECTION_FACTOR: 补偿强度，值越大补偿越强
   - MIN_PERSPECTIVE_THRESHOLD: 最小补偿阈值，小于此值不进行补偿
   - MAX_CORRECTION_RATIO: 最大补偿限制，防止过度补偿

3. 调试方法:
   - 设置 PERSPECTIVE_DEBUG = True 查看详细补偿信息
   - 观察绿色十字（原始中心）和红色十字（补偿后中心）的差异
   - 黄色线显示补偿向量的方向和大小

4. 优化建议:
   - 在不同视角下测试，确保补偿效果一致
   - 根据实际应用场景调整参数
   - 可以根据矩形大小动态调整补偿强度
"""

if __name__ == "__main__":
    print_current_config()
    print(USAGE_INSTRUCTIONS)
